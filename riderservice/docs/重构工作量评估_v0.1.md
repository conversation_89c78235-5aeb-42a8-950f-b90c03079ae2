# RiderService SDK 重构工作量评估

## 1. 评估范围

本次评估主要包含以下几个方面：

### 1.1 核心投屏逻辑重构

- 实现新的 `ProjectionManager`
- 调整 `RiderService` API
- 修改 `ConnectionManager` 以发送 `NaviMode` 指令并管理连接
- 确保底层模块 (`AutolinkControl`, `MediaProjectService`) 与新架构的集成
- 
### 1.2 测试

- 自测

### 1.3 文档

- 更新面向开发者的 API 文档和集成指南

---

## 2. 工作量估算 (人天)

| 任务模块            | 子任务                                                                       | 估算 (人天)     |
|-----------------|---------------------------------------------------------------------------|-------------|
| **1. 核心投屏逻辑重构** |                                                                           | **8 - 12**  |
|                 | 1.1 创建 `ProjectionManager.kt` 并实现核心状态机和资源管理                               | 3 - 4       |
|                 | 1.2 重构 `RiderService.kt` API (`requestNaviMode`, `stopCurrentProjection`) | 1 - 2       |
|                 | 1.3 修改 `ConnectionManager.kt` (发送 `NaviMode`, Wi-Fi 状态通知)                 | 1.5 - 2.5   |
|                 | 1.4 调整 `AutolinkControl.kt` (或其调用者) 与 `ProjectionManager` 的交互             | 1.5 - 2     |
|                 | 1.5 集成 `MediaProjectService.java`                                         | 1 - 1.5     |
| **2. 测试**       |                                                                           | **4 - 7**   |
|                 | 2.1 自测 (模拟仪表或真实仪表联调，覆盖所有 NaviMode 和连接场景)                                  | 3 - 5       |
|                 | 2.2 扫码连接场景测试 (不同二维码格式、网络环境、错误处理)                                          | 1 - 2       |
| **3. 文档**       |                                                                           | **3 - 6**   |
|                 | 3.1 创建完整的 API 参考文档 (从零开始)                                                 | 1.5 - 3     |
|                 | 3.2 编写详细的集成指南和示例代码                                                        | 1.5 - 3     |
| **总计估算**        |                                                                           | **24 - 40** |

---

## 3. 潜在风险与依赖
### 3.1 项目风险

- **客户需求变更**: 客户需求可能在开发过程中发生变化，新增或修改功能要求可能导致重新设计和开发，影响原定工作量和时间计划
 