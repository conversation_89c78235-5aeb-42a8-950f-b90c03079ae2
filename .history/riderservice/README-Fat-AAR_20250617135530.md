# Fat AAR 构建说明

## 概述

为了遵循SDK设计的最小依赖原则，我们将protobuf-java库直接打包到AAR中，避免传递依赖冲突。

## 解决的问题

1. **传递依赖冲突**：避免使用SDK的应用因protobuf版本冲突导致的问题
2. **APK体积控制**：使用方无需额外引入protobuf依赖
3. **版本兼容性**：确保SDK内部使用的protobuf版本独立于外部依赖

## 构建配置

### 依赖配置
```kotlin
// 创建一个配置来收集要嵌入的JAR
val embedJars by configurations.creating

dependencies {
    // 将protobuf-java标记为embed，编译时可用但不传递依赖
    embedJars(libs.protobuf.java)
    compileOnly(libs.protobuf.java)
    
    // 测试时需要实际的protobuf依赖
    testImplementation(libs.protobuf.java)
}
```

### 构建任务

#### fatAar
- 创建包含protobuf-java的Fat AAR
- 将protobuf类嵌入到`classes.jar`中
- 排除META-INF文件避免冲突

#### validateFatAar  
- 验证Fat AAR包含必要的protobuf类
- 检查`MessageLite`, `ByteString`, `GeneratedMessage`等核心类
- 输出AAR大小对比信息

## 使用方法

### 构建Fat AAR
```bash
./gradlew :riderservice:fatAar
```

### 验证Fat AAR
```bash  
./gradlew :riderservice:validateFatAar
```

### 构建并验证
```bash
./gradlew :riderservice:validateFatAar
```

## 输出文件

- **标准AAR**: `riderservice/build/outputs/aar/riderservice_1.0.0.aar`
- **Fat AAR**: `riderservice/build/outputs/aar/riderservice-fat_1.0.0.aar`

## 注意事项

1. **体积影响**：Fat AAR会比标准AAR大约增加 protobuf-java 的大小（约500KB）
2. **类冲突风险**：如果集成应用也使用了不同版本的protobuf，可能存在类冲突
3. **测试依赖**：单元测试仍需要显式的protobuf依赖
4. **版本管理**：确保嵌入的protobuf版本与项目需求兼容

## 集成指南

### 使用Fat AAR的应用
```kotlin
dependencies {
    implementation("com.link:riderservice-fat:1.0.0")
    // 无需额外的protobuf依赖
}
```

### 使用标准AAR的应用  
```kotlin
dependencies {
    implementation("com.link:riderservice:1.0.0")
    implementation("com.google.protobuf:protobuf-java:2.6.1")
}
```

## 最佳实践

1. **推荐使用Fat AAR**：避免依赖冲突，简化集成
2. **版本标识**：通过文件名区分标准AAR和Fat AAR
3. **文档维护**：及时更新protobuf版本信息
4. **测试验证**：每次构建后运行验证任务确保完整性 