import org.jetbrains.dokka.gradle.DokkaTask

plugins {
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
    id("com.google.protobuf")
    id("org.jetbrains.dokka")
}

fun com.android.build.api.dsl.AndroidSourceSet.proto(action: SourceDirectorySet.() -> Unit) {
    (this as? ExtensionAware)
        ?.extensions
        ?.getByName("proto")
        ?.let { it as? SourceDirectorySet }
        ?.apply(action)
}

android {
    namespace = "com.link.riderservice"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toInt()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        externalNativeBuild {
            cmake {
                cFlags("-fvisibility=hidden", "-ffunction-sections", "-fdata-sections")
                cppFlags("-fvisibility=hidden", "-ffunction-sections", "-fdata-sections -std=c++11")
                abiFilters += listOf("armeabi-v7a", "arm64-v8a")
            }
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    externalNativeBuild {
        cmake {
            version = "3.10.2"
            path("src/main/cpp/CMakeLists.txt")
        }
    }

    ndkVersion = "21.0.6113669"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }

    sourceSets {
        getByName("main"){
            proto {
                srcDir("src/main/proto")
            }
        }
    }

    packagingOptions {
        exclude("**/*.proto")
    }

    libraryVariants.all {
        outputs.all {
            packageLibraryProvider {
                archiveFileName.set("${project.name}_1.0.0.aar")
            }
        }
    }

    buildFeatures {
        buildConfig = true
    }
}

tasks.withType<DokkaTask>().configureEach {
    dokkaSourceSets {
        named("main") {
            noStdlibLink.set(false)
            noJdkLink.set(false)
            noAndroidSdkLink.set(false)

            perPackageOption {
                matchingRegex.set("(.*?)")
                suppress.set(true)
            }

            perPackageOption {
                matchingRegex.set("com.link.riderservice.api.*")
                suppress.set(false)
            }
        }
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:2.6.1"
    }
    generateProtoTasks {
        all().forEach {
            it.builtins {
                create("java")
            }
        }
    }
}

// 创建一个配置来收集要嵌入的JAR
val embedJars by configurations.creating

dependencies {
    implementation(libs.androidx.core.ktx)
    
    // 将protobuf-java标记为embed，这样可以编译但不会传递依赖
    embedJars(libs.protobuf.java)
    compileOnly(libs.protobuf.java)
    
    compileOnly(libs.protoc)
    implementation(libs.kotlinx.coroutines.android)
    implementation(libs.androidx.startup)

    testImplementation(libs.junit)
    testImplementation(libs.protobuf.java) // 测试时需要实际的protobuf依赖
    androidTestImplementation(libs.androidx.test.ext)
    androidTestImplementation(libs.espresso.core)
}

// 创建Fat AAR任务
tasks.register("fatAar", Zip::class) {
    dependsOn("bundleReleaseAar")
    
    archiveBaseName.set("${project.name}-fat")
    archiveVersion.set("1.0.0")
    archiveExtension.set("aar")
    destinationDirectory.set(file("$buildDir/outputs/aar"))
    
    from(zipTree("$buildDir/outputs/aar/${project.name}_1.0.0.aar"))
    
    // 添加嵌入的JAR文件
    from(embedJars.map { 
        zipTree(it).matching {
            exclude("META-INF/**")
        }
    }) {
        into("classes")
    }
    
    doFirst {
        println("Creating fat AAR with embedded protobuf-java...")
    }
    
    doLast {
        println("Fat AAR created: $archiveFile")
    }
}