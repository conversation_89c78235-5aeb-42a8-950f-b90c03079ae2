import org.jetbrains.dokka.gradle.DokkaTask

plugins {
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
    id("com.google.protobuf")
    id("org.jetbrains.dokka")
}

fun com.android.build.api.dsl.AndroidSourceSet.proto(action: SourceDirectorySet.() -> Unit) {
    (this as? ExtensionAware)
        ?.extensions
        ?.getByName("proto")
        ?.let { it as? SourceDirectorySet }
        ?.apply(action)
}

android {
    namespace = "com.link.riderservice"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toInt()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        externalNativeBuild {
            cmake {
                cFlags("-fvisibility=hidden", "-ffunction-sections", "-fdata-sections")
                cppFlags("-fvisibility=hidden", "-ffunction-sections", "-fdata-sections -std=c++11")
                abiFilters += listOf("armeabi-v7a", "arm64-v8a")
            }
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    externalNativeBuild {
        cmake {
            version = "3.10.2"
            path("src/main/cpp/CMakeLists.txt")
        }
    }

    ndkVersion = "21.0.6113669"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }

    sourceSets {
        getByName("main"){
            proto {
                srcDir("src/main/proto")
            }
        }
    }

    packagingOptions {
        exclude("**/*.proto")
    }

    libraryVariants.all {
        outputs.all {
            packageLibraryProvider {
                archiveFileName.set("${project.name}_1.0.0.aar")
            }
        }
    }

    buildFeatures {
        buildConfig = true
    }
}

tasks.withType<DokkaTask>().configureEach {
    dokkaSourceSets {
        named("main") {
            noStdlibLink.set(false)
            noJdkLink.set(false)
            noAndroidSdkLink.set(false)

            perPackageOption {
                matchingRegex.set("(.*?)")
                suppress.set(true)
            }

            perPackageOption {
                matchingRegex.set("com.link.riderservice.api.*")
                suppress.set(false)
            }
        }
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:2.6.1"
    }
    generateProtoTasks {
        all().forEach {
            it.builtins {
                create("java")
            }
        }
    }
}

dependencies {
    implementation(libs.androidx.core.ktx)
    
    // 将protobuf-java打包到AAR中，避免传递依赖
    api(libs.protobuf.java)
    
    compileOnly(libs.protoc)
    implementation(libs.kotlinx.coroutines.android)
    implementation(libs.androidx.startup)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.test.ext)
    androidTestImplementation(libs.espresso.core)
}

// 添加任务来将protobuf-java的JAR内容嵌入到AAR中
tasks.register("embedProtobufJar") {
    dependsOn("bundleReleaseAar")
    doLast {
        val protobufJar = configurations.api.get()
            .resolvedConfiguration
            .resolvedArtifacts
            .find { it.name == "protobuf-java" }
            ?.file
            
        if (protobufJar != null) {
            println("Found protobuf-java JAR: ${protobufJar.absolutePath}")
            
            // 解压protobuf JAR并将class文件复制到AAR中
            copy {
                from(zipTree(protobufJar))
                into("$buildDir/tmp/protobuf-classes")
                exclude("META-INF/**")
            }
            
            // 重新打包AAR
            val aarFile = file("$buildDir/outputs/aar/${project.name}_1.0.0.aar")
            if (aarFile.exists()) {
                // 创建临时目录
                val tempDir = file("$buildDir/tmp/aar-content")
                tempDir.deleteRecursively()
                tempDir.mkdirs()
                
                // 解压原AAR
                copy {
                    from(zipTree(aarFile))
                    into(tempDir)
                }
                
                // 添加protobuf classes到classes.jar
                val classesJar = file("$tempDir/classes.jar")
                val tempClassesDir = file("$buildDir/tmp/classes-expanded")
                tempClassesDir.deleteRecursively()
                tempClassesDir.mkdirs()
                
                // 解压原classes.jar
                copy {
                    from(zipTree(classesJar))
                    into(tempClassesDir)
                }
                
                // 复制protobuf classes
                copy {
                    from("$buildDir/tmp/protobuf-classes")
                    into(tempClassesDir)
                }
                
                // 重新创建classes.jar
                ant.jar(destfile: classesJar.absolutePath, basedir: tempClassesDir.absolutePath)
                
                // 重新创建AAR
                ant.zip(destfile: aarFile.absolutePath, basedir: tempDir.absolutePath)
                
                println("Successfully embedded protobuf-java into AAR")
            }
        }
    }
}

// 确保嵌入任务在构建后执行
tasks.named("bundleReleaseAar").configure {
    finalizedBy("embedProtobufJar")
}