// 验证Fat AAR的脚本
task('validateFatAar') {
    dependsOn 'fatAar'
    
    doLast {
        def fatAar = file("$buildDir/outputs/aar/${project.name}-fat_1.0.0.aar")
        
        if (!fatAar.exists()) {
            throw new GradleException("Fat AAR not found: ${fatAar.absolutePath}")
        }
        
        println "Validating Fat AAR: ${fatAar.absolutePath}"
        
        // 检查AAR内容
        def tempDir = file("$buildDir/tmp/validate")
        tempDir.deleteRecursively()
        tempDir.mkdirs()
        
        // 解压AAR
        copy {
            from zipTree(fatAar)
            into tempDir
        }
        
        // 检查classes.jar
        def classesJar = file("$tempDir/classes.jar")
        if (!classesJar.exists()) {
            throw new GradleException("classes.jar not found in AAR")
        }
        
        // 解压classes.jar并检查protobuf类
        def classesDir = file("$buildDir/tmp/validate-classes")
        classesDir.deleteRecursively()
        classesDir.mkdirs()
        
        copy {
            from zipTree(classesJar)
            into classesDir
        }
        
        // 检查关键的protobuf类
        def protobufClasses = [
            'com/google/protobuf/MessageLite.class',
            'com/google/protobuf/ByteString.class',
            'com/google/protobuf/GeneratedMessage.class'
        ]
        
        def missingClasses = []
        protobufClasses.each { className ->
            def classFile = file("$classesDir/$className")
            if (!classFile.exists()) {
                missingClasses.add(className)
            } else {
                println "✓ Found: $className"
            }
        }
        
        if (missingClasses.size() > 0) {
            throw new GradleException("Missing protobuf classes: $missingClasses")
        }
        
        println "✓ Fat AAR validation successful!"
        println "  - Size: ${fatAar.length() / 1024}KB"
        println "  - Contains all required protobuf classes"
    }
} 